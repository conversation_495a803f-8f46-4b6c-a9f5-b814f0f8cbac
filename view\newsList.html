<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>列表</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      padding: 20px;
    }

    .news-list {
      gap: 16px;
    }

    .news-item {
      gap: 12px;
    }

    .news-image {
      width: 120px;
      height: 81px;
      border-radius: 2px;
      overflow: hidden;
      flex-shrink: 0;
    }

    .news-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .news-title {
      font-weight: 400;
      font-size: 17px;
      color: #333333;
      line-height: 1.4;
    }

    .news-content {
      font-size: 16px;
      color: #666;
      line-height: 1.5;
    }

    .news-date {
      font-weight: 400;
      font-size: 15px;
      color: #999999;
      margin: 8px 0;
    }

    .nodata {
      margin-top: 30px;
      text-align: center;
      color: #999999;
    }

    .van-search {
      padding: 0 !important;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <div class="news-list flex_box flex_direction_column">
      <div class="loading" v-if="loadingShow"></div>
      <template v-else-if="dataList&&dataList.length>0">
        <template v-if="type =='featuredProduct' || type =='informationRelease'">
          <div class="search_box">
            <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
          </div>
          <div class="news-item flex_box" v-for="(item, index) in dataList" :key="index" @click="newsClick(item)">
            <div class="flex_1 flex_box flex_direction_column flex_justify_between">
              <div class="news-title two_text" v-cloak>{{ item.title || item.infoTitle }}</div>
              <div class="news-date" v-cloak>{{ formatTime(item.publishTime||item.pubTime) }}</div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="news-item flex_box" v-for="(item, index) in dataList" :key="index" @click="newsClick(item)">
            <div class="news-image" v-if="item.infoPic">
              <img :src="fileImgUrl + item.infoPic" v-cloak />
            </div>
            <div class="flex_1 flex_box flex_direction_column flex_justify_between">
              <div class="news-title two_text" v-cloak>{{ item.title }}</div>
              <div class="news-date" v-cloak>{{ formatTime(item.pubTime) }}</div>
            </div>
          </div>
        </template>
      </template>
      <template v-else>
        <div class="nodata">暂无数据</div>
      </template>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicNewsKey = '045794612289e7f9183dae2723b8e832646e3deb2b7fe39ccfd9ee2ec7fd5e219945c6ae87e6f54363a3f52b0c441a057d5e9f1bb068559e4a64d31424e1645c90' // 获取人大动态公钥
    var publicStationNewsKey = '0406e11737bd4c9ae35b20176e0e6079f0a3fbd646a8106724ca438a43d20767fb259e7bee9b2d77b2a65aa189052587c9d4d4bfcaf4b333f305792241e1db92bb' // 获取站点资讯公钥
    var publicReleaseListKey = '0498561f9e1d00d373fb262dcddb5040276d2d335677431330ebfe60314857aa3a2ab886363243dd4a994e0276b1f456e229f16d56f67567a40890172dad20f855' // 获取信息发布公钥
    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
        type: null,
        searchKeyword: '',
        loadingShow: true,
        dataList: []
      },
      async mounted () {
        var vConsole = new VConsole()
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        this.type = urlParams.get('type')
        if (this.type == 'featuredProduct') {
          this.getFeaturedList()
        } else if (this.type == 'informationRelease') {
          this.getInformationReleaseInfo()
        } else {
          this.getPeopleInfo()
        }
      },
      methods: {
        // 获取人大动态
        getPeopleInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'newsList'
          var interfacecontent = {
            pageNo: 1,
            pageSize: 99,
            query: { columnId: '', isNeedContent: false, moduleId: 1, passFlag: 1 }
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicNewsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              that.dataList = ret.data
              that.loadingShow = false
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getPeopleInfo()
            }
          })
        },

        // 获取一站一品
        getFeaturedList () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'stationNewsList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            query: { businessCode: 'pin', stationId: id },
            tableId: 'id_station_pin'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicStationNewsKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取一站一品===>', ret)
              that.dataList = ret.data
              that.loadingShow = false
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getFeaturedList()
            }
          })
        },

        // 获取信息发布
        getInformationReleaseInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawConsultRegulationList'
          var interfacecontent = {
            keyword: this.searchKeyword,
            pageNo: 1,
            pageSize: 99,
            query: { consultRegulationStatus: 2, lawPointId: id, moduleType: '1' },
            tableId: 'law_consult_regulation_info'
          }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseListKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              console.log('获取信息发布===>', ret)
              that.dataList = ret.data
              that.loadingShow = false
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInformationReleaseInfo()
            }
          })
        },

        // 打开详情
        newsClick (item) {
          window.location.href = './newsDetails.html?id=' + item.id + '&type=' + this.type || null
        },
        // 搜索
        onSearch (val) {
          this.searchKeyword = val
          if (this.type == 'featuredProduct') {
            this.getFeaturedList()
          } else if (this.type == 'informationRelease') {
            this.getInformationReleaseInfo()
          }
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>