<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background-color: #FFFFFF;
      padding: 20px;
    }

    .activity_details_title {
      font-size: 20px;
      font-weight: 800;
      color: #333333;
      line-height: 26px;
      margin-bottom: 10px;
    }

    .activity_details_status {
      background: rgba(246, 147, 28, 0.08);
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
      padding: 2px 10px;
    }

    .activity_details_bannerName {
      background: rgba(246, 147, 28, 0.08);
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
      padding: 2px 10px;
      margin-left: 10px;
    }

    .activity_details_item {
      color: rgb(51, 51, 51);
      font-weight: 600;
      font-size: 15px;
      padding: 6px 0;
    }

    .activity_details_item span {
      color: rgb(51, 51, 51);
      font-weight: normal;
    }

    .activity_details_content {
      font-size: 16px;
      color: #333333;
      line-height: 30px;
      text-indent: 2em;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <div class="activity_details_title">{{info.title}}</div>
    <div class="flex_box flex_align_center" style="margin-bottom: 10px;">
      <div class="activity_details_status">{{info.status}}</div>
      <div class="activity_details_bannerName">{{info.bannerName}}</div>
    </div>
    <div class="activity_details_item">发布联系点：<span>{{info.pointNames}}</span></div>
    <div class="activity_details_item">开始时间：<span>{{formatTime(info.startDate)}}</span></div>
    <div class="activity_details_item">结束时间：<span>{{formatTime(info.endDate)}}</span></div>
    <div class="activity_details_item">活动地点：<span>{{info.publishLocation}}</span></div>
    <div class="activity_details_content" v-html="info.content"></div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicReleaseInfoKey = '04311ef614aca94716cf94d39cda39526f5c398a5c91a901153b8239c118655c84841ef90cc2eac81566c3b95795f28e642445126e687708dc55a33fb575e161ee' // 联系点活动详情公钥

    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        info: {}
      },
      async mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          this.getInfo()
        }
      },
      methods: {
        // 获取联系点活动详情
        getInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawConsultRegulationInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              var html = ret.data.content ? ret.data.content : ''
              let newHtml = html.replace(/<img([^>]+)>/g, function (match, p1) {
                let style = 'style="width:100%; height:100%;"';
                if (/style="/.test(p1)) {
                  // 如果img标签中已经包含style属性，则替换为新的style
                  return match.replace(/style="[^"]*"/, style);
                } else {
                  // 如果img标签中不包含style属性，则添加新的style
                  return match.replace(/<img/, '<img ' + style);
                }
              })
              html = newHtml.replace(/<(?!\/?video|\/?p|\/?img|\/?br|\/?a\/?.+?>)[^<>]*>/gi, '').replace(/^[^<]*/, '');
              ret.data.content = html
              that.info = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInfo()
            }
          })
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>