<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none;
    }

    .body_box {
      background-color: #FFFFFF;
      padding: 20px;
    }

    .activity_details_title {
      font-size: 20px;
      font-weight: 800;
      color: #333333;
      line-height: 26px;
      margin-bottom: 10px;
    }

    .activity_details_status {
      background: rgba(246, 147, 28, 0.08);
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
      padding: 2px 10px;
    }

    .activity_details_bannerName {
      background: rgba(246, 147, 28, 0.08);
      font-weight: 400;
      font-size: 14px;
      color: #F6931C;
      padding: 2px 10px;
      margin-left: 10px;
    }

    .activity_details_item {
      color: rgb(51, 51, 51);
      font-weight: 600;
      font-size: 15px;
      padding: 6px 0;
    }

    .activity_details_item span {
      color: rgb(51, 51, 51);
      font-weight: normal;
    }

    .activity_details_content {
      font-size: 16px;
      color: #333333;
      line-height: 30px;
      text-indent: 2em;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <div class="activity_details_title">{{info.title}}</div>
    <div class="flex_box flex_align_center" style="margin-bottom: 10px;">
      <div class="activity_details_status">{{info.status}}</div>
      <div class="activity_details_bannerName">{{info.bannerName}}</div>
    </div>
    <div class="activity_details_item">发布联系点：<span>{{info.pointNames}}</span></div>
    <div class="activity_details_item">开始时间：<span>{{formatTime(info.startDate)}}</span></div>
    <div class="activity_details_item">结束时间：<span>{{formatTime(info.endDate)}}</span></div>
    <div class="activity_details_item">活动地点：<span>{{info.publishLocation}}</span></div>
    <div class="activity_details_content" v-html="info.content"></div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script defer type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
    var publicReleaseInfoKey = '04311ef614aca94716cf94d39cda39526f5c398a5c91a901153b8239c118655c84841ef90cc2eac81566c3b95795f28e642445126e687708dc55a33fb575e161ee' // 联系点活动详情公钥

    var id = ''
    var app = new Vue({
      el: '#app',
      data: {
        info: { "id": "1769607119960567809", "bannerId": "1769606709275291650", "title": "绿色低碳发展如何实现？听听人大代表们怎么说", "publishTime": null, "content": "<p>&nbsp;</p>\n<div id=\"Zoom\">\n<div style=\"text-indent: 2em;\">\n<div>今年的政府工作报告把加快发展新质生产力，作为首要任务，并将&ldquo;加强生态文明建设 推进绿色低碳发展&rdquo;，列为重点任务之一。围绕企业如何实现绿色低碳转型，实现高质量发展，代表们积极建言献策。</div>\n<div>今年的政府工作报告提出，大力发展绿色低碳经济。推进产业结构、能源结构、交通运输结构、城乡建设发展绿色转型。要促进节能降碳先进技术研发应用，加快形成绿色低碳供应链。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208873991.jpg\" alt=\"\" name=\"U020240310408706151472.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div>全国人大代表 张广勇：突出了高质量发展，突出了新质生产力，突出了民生保障，突出了科技创新，特别是突出了我们党和我们国家的制度优势，发挥了政府有形的手的作用。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208887974.jpg\" alt=\"\" name=\"U020240310408974949707.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div>全国人大代表 李岩松：不仅指明了我国新发展阶段，激发新动能的决定力量，也为老工业基地可持续振兴、大庆这样的资源型城市转型创新，提供了实践遵循。</div>\n<div>政府工作报告提出，推动产业链供应链优化升级。实施制造业技术改造升级工程，培育壮大先进制造业集群，创建国家新型工业化示范区，推动传统产业高端化、智能化、绿色化转型。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208899752.jpg\" alt=\"\" name=\"U020240310409190635361.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div>全国人大代表 顾祥悦：我们搭建产业链的数据化平台，从供应端、生产端、销售端和消费端，来构建上下游产业平台，使我们企业更具创新力，更具竞争力，更具发展力。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208893598.jpg\" alt=\"\" name=\"U020240310409343099041.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div>全国人大代表 刘运：中国500强企业，潍坊有6家。我们将加速重点产业链的高质量发展，2025年专精特新等优质企业超1500家，动力装备迈向世界级产业集群，加强绿色低碳技术攻关，在大规模设备更新中加大节能装备推广的应用力度。</div>\n<div>今年的政府工作报告还提出，将积极稳妥推进碳达峰、碳中和，加快建设新型能源体系。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208900768.jpg\" alt=\"\" name=\"U020240310409472965218.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div>全国人大代表 张强：作为国家电网公司一名基层代表作业工，我将继续致力于坚强主电网、智慧配电网、柔性微电网的数智化改造，不断创新新技术应用场景，用新技术为绿色能源供应赋能，为新型电力系统建设和绿色低碳发展，贡献自己的力量。</div>\n<div>同时，政府工作报告还提出，加快发展新质生产力。以科技创新推动产业创新，巩固扩大智能网联新能源汽车等产业领先优势，打造更多有国际影响力的&ldquo;中国制造&rdquo;品牌。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208904271.jpg\" alt=\"\" name=\"U020240310409650287621.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div style=\"text-indent: 2em;\">全国人大代表 朱华荣：汽车这个产业正在全方位构建我们的新质生产力，对我们国民经济的发展和新型工业化建设的支撑作用将进一步凸显。中国的汽车产业，特别是电动化智能化，已经开始从引进消化吸收向技术输出在突破。</div>\n<div><img src=\"http://www.npc.gov.cn/npc/c2/c30834/202403/W020240310411208952487.jpg\" alt=\"\" name=\"U020240310409983941292.jpg&quot;\" data-bd-imgshare-binded=\"1\" /></div>\n<div><span style=\"text-indent: 2em; font-size: 12pt;\">全国人大代表 </span><span style=\"font-family: 微软雅黑; font-size: 12pt;\">刘汉元：行业的发展，我们感觉到是绿色发展有希望。从工业硅到多晶硅，到硅片电池组件完整的实现的话，接近零碳和低碳的绿色制造链条，会有效应对欧盟的这种碳关税以及碳壁垒，然后实现全球化的应用。</span><span style=\"text-indent: 2em; font-size: 12pt;\">&nbsp;</span></div>\n</div>\n</div>\n<div style=\"text-align: right;\">编 辑： 夏红真</div>\n<div style=\"text-align: right;\">责 编： 刘静波</div>", "sort": 1, "attachmentIds": "", "isTop": 0, "isIgnoreArea": 0, "publishLocation": "", "areaId": "371500", "createBy": "1", "createDate": 1710742189767, "updateBy": "1", "updateDate": 1710742189767, "delFlag": 0, "lawPointId": "1769568285222793217", "images": null, "startDate": 1710000000000, "endDate": 1710086400000, "moduleType": "2", "consultRegulationStatus": "2", "bannerName": "座谈会", "createUserName": "Admin", "attachments": null, "pointNames": "xx省人大常委会基层立法联系点", "imageList": null, "status": "已结束", "commentCount": null },
        fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/'
      },
      async mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        if (id) {
          // this.getInfo()
        }
      },
      methods: {
        // 获取联系点活动详情
        getInfo () {
          var that = this
          var appid = 'lcsszrduwgvg'
          var interfaceid = 'lawConsultRegulationInfo'
          var interfacecontent = { detailId: id }
          let extraData = {
            header: { 'u-login-areaId': '371500' }
          }
          let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicReleaseInfoKey)
          vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
            try {
              var ret = JSON.parse(SM.decrypt(res, privateKey))
              // var html = ret.data.content ? ret.data.content : ''
              // let newHtml = html.replace(/<img([^>]+)>/g, function (match, p1) {
              //   let style = 'style="width:100%; height:100%;"';
              //   if (/style="/.test(p1)) {
              //     // 如果img标签中已经包含style属性，则替换为新的style
              //     return match.replace(/style="[^"]*"/, style);
              //   } else {
              //     // 如果img标签中不包含style属性，则添加新的style
              //     return match.replace(/<img/, '<img ' + style);
              //   }
              // })
              // console.log('newHtml===>', newHtml)
              // html = newHtml
              // html = newHtml.replace(/<(?!\/?video|\/?p|\/?img|\/?br|\/?a\/?.+?>)[^<>]*>/gi, '').replace(/^[^<]*/, '');
              // ret.data.content = html
              that.info = ret.data
            } catch (error) {
              // 在这里处理异常情况
              console.log('解析JSON时发生错误：', error)
              that.getInfo()
            }
          })
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>