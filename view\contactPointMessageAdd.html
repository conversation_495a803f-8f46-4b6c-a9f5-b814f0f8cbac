<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>留言</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none
    }

    .body_box {
      background: #fff;
      height: 100vh;
    }

    .form-container {
      padding: 20px;
    }

    .form-item {
      margin-bottom: 20px;
    }

    .form-label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      display: block;
    }

    .required {
      color: #ff4444;
    }

    .title-input {
      width: 100%;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      padding: 12px;
      font-size: 14px;
      box-sizing: border-box;
    }

    .title-input::placeholder {
      color: #999;
    }

    .radio-group {
      display: flex;
      gap: 20px;
    }

    .radio-item {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .radio-input {
      width: 15px;
      height: 15px;
    }

    .radio-label {
      font-size: 14px;
      color: #333;
    }

    .textarea-container {
      position: relative;
    }

    .content-textarea {
      width: 100%;
      min-height: 300px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      padding: 12px;
      padding-bottom: 40px;
      /* 为字数统计留出空间 */
      font-size: 14px;
      box-sizing: border-box;
      resize: none;
      line-height: 1.5;
    }

    .content-textarea::placeholder {
      color: #ccc;
    }

    .char-count {
      position: absolute;
      bottom: 12px;
      right: 12px;
      font-size: 12px;
      color: #999;
      background: white;
      padding: 2px 4px;
    }

    .button-container {
      display: flex;
      gap: 12px;
      padding: 16px;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: white;
      border-top: 1px solid #e5e5e5;
    }

    .btn {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      border: none;
      font-size: 14px;
    }

    .btn-cancel {
      background: #f5f5f5;
      color: #666;
    }

    .btn-submit {
      background: #1976d2;
      color: white;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <div class="form-container">
      <!-- 标题输入 -->
      <div class="form-item">
        <label class="form-label">
          <span class="required">*</span>标题
        </label>
        <input type="text" class="title-input" placeholder="请输入标题，2-100字" v-model="title" maxlength="100" />
      </div>

      <!-- 公开意愿 -->
      <div class="form-item">
        <label class="form-label">
          <span class="required">*</span>公开意愿
        </label>
        <div class="radio-group">
          <div class="radio-item">
            <input type="radio" class="radio-input" id="public" value="1" v-model="isPublic" />
            <label for="public" class="radio-label">公开</label>
          </div>
          <div class="radio-item">
            <input type="radio" class="radio-input" id="private" value="0" v-model="isPublic" />
            <label for="private" class="radio-label">不公开</label>
          </div>
        </div>
      </div>

      <!-- 留言内容 -->
      <div class="form-item">
        <label class="form-label">
          <span class="required">*</span>留言内容
        </label>
        <div class="textarea-container">
          <textarea class="content-textarea" placeholder="请输入留言内容" v-model="content" maxlength="2000"></textarea>
          <div class="char-count">已输入{{ contentLength }}字</div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="button-container">
      <button class="btn btn-cancel" @click="cancel">取消</button>
      <button class="btn btn-submit" @click="submit">发送</button>
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4' // 私钥
    var publicThemeLetterAddKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 提交留言公钥

    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        title: '',
        isPublic: '1', // 默认选择公开
        content: ''
      },
      computed: {
        contentLength () {
          return this.content.length
        }
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
      },
      methods: {
        cancel () {
          // 返回上一页或关闭页面
          if (window.history.length > 1) {
            window.history.back()
          } else {
            window.close()
          }
        },
        submit () {
          // 验证标题长度
          if (this.title.trim().length < 2) {
            vant.Toast('标题至少需要2个字符')
            return
          }
          if (this.title.trim().length > 100) {
            vant.Toast('标题不能超过100个字符')
            return
          }
          // 验证内容
          if (this.content.trim().length === 0) {
            vant.Toast('请输入留言内容')
            return
          }
          // 显示加载中提示
          vant.Toast.loading({
            mask: true,
            message: '提交中...'
          });
          // 这里可以添加提交逻辑
          console.log('提交数据:', {
            title: this.title.trim(),
            isPublic: this.isPublic,
            content: this.content.trim(),
            contactPointId: id
          })
          // 提交成功后的处理
          vant.Toast.clear()
          vant.Toast.success('留言提交成功！')
          setTimeout(() => {
            this.cancel()
          }, 1000)
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>