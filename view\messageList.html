<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>我的留言</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none
    }

    .body_box {
      background: #f5f6fa;
      height: 100vh;
    }

    .van-search {
      padding: 0;
    }

    .search_box {
      padding: 10px;
      background: #fff
    }

    .message_list {
      margin: 10px;
    }

    .message_item {
      background: #FFFFFF;
      box-shadow: 0px 2px 10px 0px rgba(24, 64, 118, 0.08);
      padding: 10px;
      margin-bottom: 10px;
      align-items: flex-end;
    }

    .message_icon {
      width: 20px;
      height: 20px;
    }

    .message_title {
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      margin-left: 4px;
    }

    .message_info {
      font-weight: 400;
      font-size: 14px;
      color: #999999;
      margin-top: 10px;
    }

    .sender {
      margin-right: 10px;
    }

    .message_right {
      align-items: flex-end;
      position: relative;
    }

    .status_replied {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 2px;
      background: rgba(80, 198, 20, 0.1);
      color: rgb(80, 198, 20);
    }

    .status_unreplied {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 2px;
      color: #F6631C;
      background: rgba(246, 99, 28, 0.08);
    }
  </style>
</head>

<body>
  <div class="body_box" id="app">
    <van-tabs v-model="active" shrink color="#0D75FF" swipeable>
      <van-tab v-for="(item,index) in activeData" :key="item.id" :name="item.id" :title="item.value">
        <div class="search_box">
          <van-search v-model="searchKeyword" placeholder="请输入关键词" @search="onSearch" />
        </div>
        <van-list v-model:loading="loading" :finished="finished" :immediate-check="false" finished-text="没有更多了"
          offset="52" @load="onLoad">
          <div class="message_list" v-cloak>
            <div class="flex_box message_item" v-for="item in messageList" :key="item.id" @click="openDetails(item)">
              <div class="flex_1">
                <div class="flex_box flex_align_center">
                  <img class="message_icon" src="../img/icon_read.png" />
                  <div class="message_title one_text">{{item.title}}</div>
                </div>
                <div class="message_info">
                  <span class="sender">写信人：{{item.senderUserName}}</span>
                  <span class="time">{{formatTime(item.receiveTime)}}</span>
                </div>
              </div>
              <div class="message_right flex_box flex_justify_between flex_direction_column">
                <span :class="item.hasAnswer ? 'status_replied' :'status_unreplied'">{{item.hasAnswer ? '已回复' :
                  '未回复'}}</span>
              </div>
            </div>
          </div>
        </van-list>
      </van-tab>
    </van-tabs>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4' // 私钥
    var publicMessageCountKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 留言统计公钥
    var publicMessageListKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 留言列表公钥

    var id = '', type = ''
    var app = new Vue({
      el: "#app",
      data: {
        active: '1',
        activeData: [
          { id: '1', value: '已回复' },
          { id: '0', value: '未回复' }
        ],
        searchKeyword: '',
        loading: false,
        finished: false,
        pageNo: 1,
        pageSize: 15,
        messageList: [{ "id": "1986304265240473601", "serialNumber": "20251106000001", "title": "测试", "content": "11", "letterMobile": null, "receiveTime": 1762406815236, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 0, "receiverId": "0", "senderId": "o9RxP1wJqkzomDnvOg1fxlaMdUFs", "senderUserName": "耿123", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "PUBLIC", "businessId": null, "createDate": 1762406815239, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": null, "senderPhoto": null, "receiverName": null, "receiverTel": null, "email": null, "userRoles": ["普通用户"], "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } }],
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        type = urlParams.get('type')
        this.getMessageList()
      },
      methods: {
        // 获取留言列表
        getMessageList (status) {

        },
        // 下拉加载
        onLoad () {
          if (this.flag) {
            this.getMessageList()
          }
        },
        // 搜索
        onSearch (val) {
          this.searchKeyword = val
          this.getMessageList()
        },
        // 打开留言详情
        openDetails (item) {
          window.location.href = './messageDetails.html?id=' + item.id
        },
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>