<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>聊城人大</title>
  <link rel="stylesheet" href="./css/vant.css" />
  <link rel="stylesheet" href="./css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none
    }

    .body_box {
      width: 100%;
      height: 100%;
      background: #f5f6fa;
    }

    .content-area {
      padding-bottom: 60px;
    }

    /* 轮播图 */
    .swiper-container {
      position: relative;
    }

    .banner-swiper {
      height: 240px;
    }

    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .banner-overlay {
      margin: -32px 16px 16px 16px;
      background: #fff;
      border-radius: 8px;
      padding: 5px 10px;
      position: relative;
      height: 60px;
    }

    .banner-icon {
      width: 45px;
      height: 42px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .banner-title {
      flex: 1;
      font-size: 17px;
      color: #333333;
      line-height: 1.4;
      font-weight: 500;
    }

    .swiper-dots {
      gap: 6px;
      padding-bottom: 10px;
    }

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #d0d0d0;
      transition: background 0.3s ease;
    }

    .dot.active {
      background: #1989fa;
    }

    /* 核心服务 */
    .core-service {
      margin: 12px;
      background: #fff;
      border-radius: 10px;
      padding: 14px 14px 20px 14px;
    }

    .service-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 12px;
    }

    .service-item {
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      min-height: 80px;
    }

    /* 特殊卡片：人大动态（背景图铺满） */
    .service-bg-image {
      position: absolute;
      inset: 0;
    }

    .bg-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .service-content-special {
      position: relative;
      padding: 12px;
    }

    .service-name-special {
      font-size: 16px;
      font-weight: 600;
    }

    .service-desc-special {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.85);
      margin-top: 4px;
    }

    /* 普通卡片 */
    .service-item-normal {
      align-items: stretch;
    }

    .service-content-normal {
      padding: 12px;
    }

    .service-name-normal {
      font-size: 16px;
      font-weight: 600;
    }

    .service-desc-normal {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.55);
      margin-top: 4px;
    }

    .service-icon-corner {
      position: absolute;
      right: 6px;
      bottom: 6px;
      width: 44px;
      height: 44px;
      opacity: 0.9;
    }

    .corner-icon {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    /* 人大动态样式 */
    .news-section {
      margin: 12px 12px 20px 12px;
      background: #fff;
      border-radius: 10px;
      padding: 11px 13px;
    }

    .news-list {
      gap: 16px;
    }

    .news-item {
      gap: 12px;
    }

    .news-image {
      width: 120px;
      height: 81px;
      border-radius: 2px;
      overflow: hidden;
      flex-shrink: 0;
    }

    .news-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .news-title {
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .news-date {
      font-weight: 400;
      font-size: 15px;
      color: #999999;
      margin: 8px 0;
    }

    .more-text {
      font-size: 14px;
      color: #666;
    }

    .more-img {
      width: 24px;
      height: 24px;
    }
  </style>
</head>

<link>
<div class="body_box" id="app">
  <!-- 首页内容 -->
  <template v-show="activeTab === 'home'">
    <!-- 轮播图 -->
    <div class="swiper-container">
      <van-swipe class="banner-swiper" :autoplay="3000" :show-indicators="false" :duration="500"
        @change="onSwiperChange">
        <van-swipe-item v-for="(item, index) in bannerList" :key="index" @click="bannerClick(item.id)">
          <img :src="item.infoPic?fileImgUrl+item.infoPic:''" class="banner-image" @click="bannerClick(item.id)" />
        </van-swipe-item>
      </van-swipe>

      <!-- 轮播图信息卡片 -->
      <div class="banner-overlay flex_box flex_align_center" @click="bannerClick(bannerList[currentSwiperIndex].id)">
        <img src="./img/key_meeting.png" class="banner-icon" />
        <div class="banner-title" v-cloak>{{ bannerList[currentSwiperIndex]?.title }}</div>
      </div>

      <!-- 轮播图指示器 -->
      <div class="swiper-dots flex_box flex_justify_center">
        <div class="dot" :class="{ active: currentSwiperIndex == index }" v-for="(item, index) in bannerList"
          :key="index"></div>
      </div>
    </div>
    <!-- 核心服务 -->
    <div class="core-service">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <div class="section-title">核心服务</div>
      </div>
      <div class="service-grid">
        <!-- 人大动态 - 特殊样式，背景图片 -->
        <div class="service-item" @click="serviceClick('1')">
          <div class="service-bg-image">
            <img src="./img/npc_dynamic.png" class="bg-image" />
          </div>
          <div class="service-content-special">
            <div class="service-name-special" style="color: #fff;">人大动态</div>
            <div class="service-desc-special">最新人大要闻</div>
          </div>
        </div>

        <!-- 代表工作站 -->
        <div class="service-item service-item-normal flex_box"
          style="background: linear-gradient(135deg, #F4E4C1 0%, #E6D3A3 100%);" @click="serviceClick('2')">
          <div class="service-content-normal flex_direction_column flex_box">
            <div class="service-name-normal" style="color: #CE8500;">代表工作站</div>
            <div class="service-desc-normal">代表联系群众</div>
          </div>
          <div class="service-icon-corner">
            <img src="./img/workstation.png" class="corner-icon" />
          </div>
        </div>

        <!-- 立法联系点 -->
        <div class="service-item service-item-normal flex_box"
          style="background: linear-gradient(135deg, #C8E6F5 0%, #A8D0E6 100%);" @click="serviceClick('3')">
          <div class="service-content-normal flex_direction_column flex_box">
            <div class="service-name-normal" style="color: #0A67A3;">立法联系点</div>
            <div class="service-desc-normal">法规征询意见</div>
          </div>
          <div class="service-icon-corner">
            <img src="./img/legislation_point.png" class="corner-icon" />
          </div>
        </div>

        <!-- 建议公开 -->
        <div class="service-item service-item-normal flex_box"
          style="background: linear-gradient(135deg, #C8F2E6 0%, #A8E6D3 100%);" @click="serviceClick('4')">
          <div class="service-content-normal flex_direction_column flex_box">
            <div class="service-name-normal" style="color: #067786;">建议公开</div>
            <div class="service-desc-normal">代表建议查询</div>
          </div>
          <div class="service-icon-corner">
            <img src="./img/suggest_public.png" class="corner-icon" />
          </div>
        </div>
      </div>
    </div>
    <!-- 人大动态 -->
    <div class="news-section">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <div class="section-title">人大动态</div>
        <div class="flex_box flex_align_center" @click="moreNews">
          <span class="more-text">更多</span>
          <img src="./img/arrow_right.png" class="more-img" />
        </div>
      </div>
      <div class="news-list flex_box flex_direction_column" v-cloak>
        <div class="news-item flex_box" v-for="(item, index) in newsList" :key="index" @click="newsClick(item.id)">
          <div class="news-image" v-if="item.infoPic">
            <img :src="fileImgUrl+item.infoPic" v-cloak />
          </div>
          <div class="flex_1 flex_box flex_direction_column flex_justify_between">
            <div class="news-title" v-cloak>{{ item.title }}</div>
            <div class="news-date" v-cloak>{{ formatTime(item.pubTime) }}</div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- 我的页面内容 -->
  <!-- <template v-show="activeTab === 'my'">
      <div style="padding: 20px; text-align: center;">
        <h3>我的页面</h3>
        <p>这里是我的页面内容</p>
      </div>
    </template> -->

  <!-- 底部导航栏 -->
  <!-- <div class="bottom-tab-bar">
    <div class="tab-item" :class="{ active: activeTab === 'home' }" @click="switchTab('home')">
      <div class="tab-icon home-icon" :class="{ active: activeTab === 'home' }"></div>
      <div class="tab-text">首页</div>
    </div>
    <div class="tab-item" :class="{ active: activeTab === 'my' }" @click="switchTab('my')">
      <div class="tab-icon my-icon"></div>
      <div class="tab-text">我的</div>
    </div>
  </div> -->
</div>
<!-- <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script> -->
<script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
<script defer type="text/javascript" src="./js/aes.js"></script>
<script type="text/javascript" src="./js/userinfo.js"></script>
<script src="./js/SM.js" type="text/javascript" charset="utf-8"></script>
<script src="./js/vue.min.js"></script>
<script src="./js/vant.min.js"></script>
<script>
  var privateKey = '00834409aff7fe64af1c1488b23c671eddbaee49d2ecc5025d6f5f2db9fe8a1da7' // 私钥
  var publicNewsKey = '045794612289e7f9183dae2723b8e832646e3deb2b7fe39ccfd9ee2ec7fd5e219945c6ae87e6f54363a3f52b0c441a057d5e9f1bb068559e4a64d31424e1645c90' 	// 获取首页资讯轮播图公钥
  var app = new Vue({
    el: "#app",
    data: {
      activeTab: 'home', // 当前激活的tab
      currentSwiperIndex: 0, // 当前轮播图索引
      fileImgUrl: 'https://szrd.renda.liaocheng.gov.cn:8443/lzt/image/',
      bannerList: [],
      newsCarouselList: [],
      peopleInfoList: [],
      newsList: []
    },
    mounted () {
      // var vConsole = new VConsole()
      console.log(localStorage.getItem('gname'))
      console.log(localStorage.getItem('gphone'))
      this.getNewsCarousel()
      this.getPeopleInfo()
    },
    methods: {
      // 切换底部tab
      switchTab (tab) {
        this.activeTab = tab
      },

      // 获取首页资讯轮播图
      getNewsCarousel () {
        var that = this
        var appid = 'lcsszrduwgvg'
        var interfaceid = 'newsList'
        var interfacecontent = {
          pageNo: 1,
          pageSize: 10,
          query: { columnId: '1980924013957943298', isNeedContent: false, moduleId: 1, passFlag: 1 }
        }
        let extraData = {
          header: { 'u-login-areaId': '371500' }
        }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicNewsKey)
        vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
          try {
            var ret = JSON.parse(SM.decrypt(res, privateKey))
            that.bannerList = ret.data
          } catch (error) {
            // 在这里处理异常情况
            console.log('解析JSON时发生错误：', error)
            that.getNewsCarousel()
          }
        })
      },

      // 轮播图切换事件
      onSwiperChange (index) {
        this.currentSwiperIndex = index
      },

      // 轮播图点击事件
      bannerClick (id) {
        window.location.href = './view/newsDetails.html?id=' + id
      },

      // 核心服务点击
      serviceClick (id) {
        console.log('点击核心服务:', id)
        switch (id) {
          case '1':
            // 人大动态
            window.location.href = './view/newsList.html'
            break;
          case '2':
            // 代表工作站
            window.location.href = './view/workstation.html'
            break;
          case '3':
            // 立法联系点
            window.location.href = './view/contactPoint.html'
            break;
          case '4':
            // 建议公开
            window.location.href = './view/publicSuggestion.html'
            break;
        }
      },

      // 获取人大动态
      getPeopleInfo () {
        var that = this
        var appid = 'lcsszrduwgvg'
        var interfaceid = 'newsList'
        var interfacecontent = {
          pageNo: 1,
          pageSize: 3,
          query: { columnId: '', isNeedContent: false, moduleId: 1, passFlag: 1 }
        }
        let extraData = {
          header: { 'u-login-areaId': '371500' }
        }
        let biz_content = SM.encrypt(JSON.stringify(interfacecontent), publicNewsKey)
        vaildInterfacefn(appid, interfaceid, biz_content, '2', extraData).then(res => {
          try {
            var ret = JSON.parse(SM.decrypt(res, privateKey))
            that.newsList = ret.data
          } catch (error) {
            // 在这里处理异常情况
            console.log('解析JSON时发生错误：', error)
            that.getPeopleInfo()
          }
        })
      },

      // 人大动态点击
      newsClick (id) {
        window.location.href = './view/newsDetails.html?id=' + id
      },

      // 更多人大动态
      moreNews () {
        window.location.href = './view/newsList.html'
      },
    }
  })

  window.onscroll = function () {
    var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    app.scrollTop = scrollTop
  }
  function formatTime (date) {
    var date = new Date(date)
    var year = date.getFullYear()
    var month = date.getMonth() + 1
    var day = date.getDate()
    var hour = date.getHours()
    var minute = date.getMinutes()
    var second = date.getSeconds()
    // 返回年月日时分秒
    return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
  }

  function formatNumber (n) {
    n = n.toString()
    return n[1] ? n : '0' + n
  }
</script>

</body>

</html>