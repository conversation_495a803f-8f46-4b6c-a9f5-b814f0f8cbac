<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>留言详情</title>
  <link rel="stylesheet" href="../css/vant.css" />
  <link rel="stylesheet" href="../css/common.css" />
  <style type="text/css">
    [v-cloak] {
      display: none
    }

    .body_box {
      background: #ffffff;
      min-height: 100vh;
    }

    /* 详情信息样式 */
    .message_details {
      padding: 20px;
    }

    .message_details_title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      margin-bottom: 12px;
      line-height: 1.4;
    }

    .message_details_info {
      margin-bottom: 12px;
    }

    .message_details_time {
      font-size: 14px;
      color: #999;
    }

    .message_details_status {
      font-size: 14px;
      color: #ff8800;
      background: #fff3e0;
      padding: 4px 8px;
      border-radius: 4px;
    }

    .message_details_status.replied {
      color: #4caf50;
      background: #e8f5e8;
    }

    .message_details_point,
    .message_details_will {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .message_details_content {
      font-size: 16px;
      color: #333;
      line-height: 1.6;
      margin-top: 16px;
    }

    /* 回复列表样式 */
    .reply_box {
      padding: 0 20px;
    }

    .reply_header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }

    .reply_line {
      width: 4px;
      height: 16px;
      background: #1976d2;
      margin-right: 8px;
    }

    .reply_title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }

    .reply_list {
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 16px;
      margin-bottom: 16px;
    }

    .reply_list:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .reply_name_time {
      margin-bottom: 8px;
    }

    .reply_name {
      font-size: 15px;
    }

    .reply_time {
      font-size: 14px;
      color: #999;
    }

    .reply_content {
      font-size: 15px;
      color: #333;
      line-height: 1.5;
    }
  </style>
</head>

<body>
  <div class="body_box" id="app" v-cloak>
    <!-- 详情信息 -->
    <div class="message_details" v-if="info">
      <div class="message_details_title">{{ info.title }}</div>
      <div class="message_details_info flex_box flex_align_center flex_justify_between">
        <div class="message_details_time">{{ formatTime(info.receiveTime) }}</div>
        <div class="message_details_status" :class="{ replied: info.hasAnswer }">
          {{ info.hasAnswer ? '已回复' : '未回复' }}
        </div>
      </div>
      <div class="message_details_point">
        留言联系点：{{ info.extProperties && info.extProperties.extBlueName || '未知' }}
      </div>
      <div class="message_details_will">
        公开意愿：{{ info.isOpenAdmin ? '公开' : '不公开' }}
      </div>
      <div class="message_details_content" v-html="info.content"></div>
    </div>
    <!-- 回复列表 -->
    <div class="reply_box" v-if="answerList && answerList.length > 0">
      <div class="section-header flex_box flex_align_center">
        <div class="section-line"></div>
        <div class="section-title">回复</div>
      </div>
      <div class="reply_list" v-for="(item, index) in answerList" :key="index">
        <div class="reply_name_time flex_box flex_align_center flex_justify_between">
          <div class="reply_name">{{ item.answerUserName }}</div>
          <div class="reply_time">{{ formatTime(item.answerTime) }}</div>
        </div>
        <div class="reply_content">{{ item.content }}</div>
      </div>
    </div>
    <!-- 加载状态 -->
    <div v-if="loading" style="text-align: center; padding: 40px 0; color: #999;">
      加载中...
    </div>
  </div>
  <script type="text/javascript" src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"></script>
  <script type="text/javascript" src="https://isdapp.shandong.gov.cn/jmopen/jssdk/index.js"></script>
  <script type="text/javascript" src="../js/aes.js"></script>
  <script src="../js/SM.js" type="text/javascript" charset="utf-8"></script>
  <script src="../js/vue.min.js"></script>
  <script src="../js/vant.min.js"></script>
  <script>
    var areaKey = '00871c7d56551fb20bde67a129649e7faf95c05d9203d47ed6de529df5722303b4' // 私钥
    var publicMessageInfoKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 留言详情公钥
    var publicMessageAnswerKey = '045b4e75032df41d536233631f426ef247ac738049ab291d1612df02801cb1f37cf9ef7949e5f00cecdb55411b3822deef5b03b8ab48fa8b45ebc546d1d88c32f5' // 留言回复公钥

    var id = ''
    var app = new Vue({
      el: "#app",
      data: {
        loading: true,
        info: { "id": "1981518950734790658", "serialNumber": "20251024000001", "title": "测试", "content": "1", "letterMobile": "***********", "receiveTime": 1761265907352, "receiveLetterType": { "value": null, "label": null, "name": null, "dictCode": null }, "hasAnswer": 1, "receiverId": "0", "senderId": "1", "senderUserName": "Admin", "senderType": null, "isOpenAdmin": 1, "isOpenSender": 1, "checkStatus": 1, "terminalName": "APP", "businessId": null, "createDate": 1761265907355, "businessCode": "law_contact_point", "extYellow": { "value": null, "label": null, "name": null, "dictCode": null }, "extRed": "1", "extBlue": "1981253626277953538", "extGreen": null, "senderHeadImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "senderPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "receiverName": "", "receiverTel": null, "email": null, "userRoles": null, "answers": null, "extProperties": { "extBlueName": "测试立法联系点" } },
        answerList: [{ "id": "1981519073896333313", "letterId": "1981518950734790658", "answerUserId": "1", "content": "测试", "attachmentIds": [], "checkStatus": 0, "answerTime": 1761265936718, "userRoles": null, "fileInfos": null, "answerUserName": "Admin", "answerPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "headImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png" }, { "id": "1981519169044119554", "letterId": "1981518950734790658", "answerUserId": "1", "content": "111", "attachmentIds": [], "checkStatus": 0, "answerTime": 1761265959403, "userRoles": null, "fileInfos": null, "answerUserName": "Admin", "answerPhoto": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png", "headImg": "632c74f7-5843-4f6b-b02f-b4e0c01d977f.png" }]
      },
      mounted () {
        var vConsole = new VConsole() // 初始化
        const urlParams = new URLSearchParams(window.location.search)
        id = urlParams.get('id')
        // 加载留言详情
        this.loadMessageDetails()
      },
      methods: {
        // 加载留言详情
        loadMessageDetails () {
          setTimeout(() => {
            this.loading = false
          }, 1000)
        }
      }
    })
    function formatTime (date) {
      var date = new Date(date)
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var day = date.getDate()
      var hour = date.getHours()
      var minute = date.getMinutes()
      var second = date.getSeconds()
      // 返回年月日时分秒
      return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
    }

    function formatNumber (n) {
      n = n.toString()
      return n[1] ? n : '0' + n
    }
  </script>
</body>

</html>